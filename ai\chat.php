<?php
header('Content-Type: application/json');
require_once 'GeminiAI.php';
require_once '../includes/session.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['message']) || empty(trim($input['message']))) {
        throw new Exception('Message is required');
    }

    $_SESSION['current_user_message'] = $input['message'];

    $ai = new GeminiAI();
    $response = $ai->generateResponse($input['message']);
    $formatted = $ai->formatResponse($response);

    $formatted['user_message'] = $input['message'];

    echo json_encode($formatted);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'type' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
