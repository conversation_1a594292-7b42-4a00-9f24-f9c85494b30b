<?php
header('Content-Type: application/json');
require_once 'GeminiAI.php';
require_once '../includes/session.php';

// Ensure session is started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

try {
    // Check if it's a POST request
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get the raw POST data
    $input = json_decode(file_get_contents('php://input'), true);

    // Validate input
    if (!isset($input['message']) || empty(trim($input['message']))) {
        throw new Exception('Message is required');
    }

    // Store user message in session and initialize AI
    $_SESSION['current_user_message'] = $input['message'];
    
    // Pass complete session data to AI context
    $sessionContext = [
        'modid' => $_SESSION['modid'] ?? null,
        'role' => $_SESSION['role'] ?? null,
        'usertype' => $_SESSION['usertype'] ?? null,
        'username' => ($_SESSION['firstname'] ?? '') . ' ' . ($_SESSION['lastname'] ?? ''),
        'uaid' => $_SESSION['uaid'] ?? null,
        'position' => $_SESSION['position'] ?? null,
        'session_id' => session_id()
    ];
    
    $ai = new GeminiAI();
    $response = $ai->generateResponse($input['message'], $sessionContext);
    $formatted = $ai->formatResponse($response);

    // Add user message to response for client-side handling
    $formatted['user_message'] = $input['message'];
    
    // Send response
    echo json_encode($formatted);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'type' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
