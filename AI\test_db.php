<?php
/**
 * Test Database Connection and Student Data
 */
require_once '../core/dbcon.ini';
require_once '../query/ai.qry';

echo "Testing Database Connection and Student Data\n";
echo "==========================================\n\n";

try {
    global $server1, $username1, $password1, $database1;
    $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connection successful\n\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

$aifnc = new AIFNC();

// Test getting all students using the correct table
echo "Testing cdas_student table:\n";
echo "-------------------------\n";
try {
    $stmt = $db->prepare("SELECT * FROM cdas_student LIMIT 1");
    $stmt->execute();
    $sample = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($sample) {
        echo "Sample student data structure:\n";
        print_r($sample);
        echo "\nColumn names: " . implode(', ', array_keys($sample)) . "\n";
    } else {
        echo "No students found in cdas_student table\n";
    }

    // Get all students
    $stmt = $db->prepare("SELECT * FROM cdas_student");
    $stmt->execute();
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "\nTotal students in database: " . count($students) . "\n";

    if (!empty($students)) {
        echo "\nAll students:\n";
        foreach ($students as $student) {
            echo "- ";
            foreach ($student as $key => $value) {
                echo "$key: $value, ";
            }
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "Error getting students: " . $e->getMessage() . "\n";
}

echo "\n\nTesting search_student_by_name('nessa'):\n";
echo "---------------------------------------\n";
try {
    $searchResults = $aifnc->search_student_by_name($db, 'nessa', 1);
    echo "Found " . count($searchResults) . " students matching 'nessa'\n";

    if (!empty($searchResults)) {
        foreach ($searchResults as $student) {
            echo "- " . ($student['Last_Name'] ?? 'N/A') . " " . ($student['First_Name'] ?? 'N/A') .
                 " (Course: " . ($student['Course'] ?? 'N/A') . ", Year: " . ($student['Year'] ?? 'N/A') . ")\n";
        }
    }
} catch (Exception $e) {
    echo "Error searching students: " . $e->getMessage() . "\n";
}

echo "\n\nTesting get_student_data():\n";
echo "--------------------------\n";
try {
    $studentData = $aifnc->get_student_data($db, 1);
    echo $studentData;
} catch (Exception $e) {
    echo "Error getting student data: " . $e->getMessage() . "\n";
}

echo "\n==========================================\n";
echo "Database Test Complete!\n";
?>
