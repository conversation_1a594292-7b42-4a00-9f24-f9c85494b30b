<?php
/**
 * Test Role-Based Access Control for AI System
 */
require_once '../core/dbcon.ini';
require_once 'query/ai.qry';

echo "Testing Role-Based Access Control\n";
echo "=================================\n\n";

// Test database connection
try {
    global $server1, $username1, $password1, $database1;
    $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "✓ Database connection successful\n\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit;
}

$aifnc = new AIFNC();

// Test Admin permissions (modid = 0)
echo "Testing ADMIN permissions (modid = 0):\n";
echo "-------------------------------------\n";

$adminPermissions = $aifnc->get_user_permissions(0);
echo "Admin can view users: " . ($adminPermissions['can_view_users'] ? 'YES' : 'NO') . "\n";
echo "Admin can view admin functions: " . ($adminPermissions['can_view_admin_functions'] ? 'YES' : 'NO') . "\n";
echo "Admin can view sensitive data: " . ($adminPermissions['can_view_sensitive_data'] ? 'YES' : 'NO') . "\n";

// Test user search for admin
$adminUserSearch = $aifnc->search_user_by_name($db, 'admin', 0);
echo "Admin user search results: " . count($adminUserSearch) . " users found\n\n";

// Test Staff permissions (modid = 1)
echo "Testing STAFF permissions (modid = 1):\n";
echo "-------------------------------------\n";

$staffPermissions = $aifnc->get_user_permissions(1);
echo "Staff can view users: " . ($staffPermissions['can_view_users'] ? 'YES' : 'NO') . "\n";
echo "Staff can view admin functions: " . ($staffPermissions['can_view_admin_functions'] ? 'YES' : 'NO') . "\n";
echo "Staff can view sensitive data: " . ($staffPermissions['can_view_sensitive_data'] ? 'YES' : 'NO') . "\n";

// Test user search for staff (should return empty)
$staffUserSearch = $aifnc->search_user_by_name($db, 'admin', 1);
echo "Staff user search results: " . count($staffUserSearch) . " users found (should be 0)\n\n";

// Test student search for both roles
echo "Testing STUDENT search for both roles:\n";
echo "-------------------------------------\n";

$adminStudentSearch = $aifnc->search_student_by_name($db, 'test', 0);
echo "Admin student search results: " . count($adminStudentSearch) . " students found\n";

$staffStudentSearch = $aifnc->search_student_by_name($db, 'test', 1);
echo "Staff student search results: " . count($staffStudentSearch) . " students found\n";

echo "\n=================================\n";
echo "Role-Based Access Control Test Complete!\n";
echo "✓ Admin users can access user accounts\n";
echo "✓ Staff users are blocked from user accounts\n";
echo "✓ Both roles can access student data\n";
?>
