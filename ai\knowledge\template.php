<?php
/**
 * Knowledge Template
 * Use this template to create new knowledge sources for the AI
 * Copy this file and rename it to match your knowledge topic
 */

function getTemplateKnowledge() {
    return [
        'title' => 'Knowledge Title',  // Replace with your knowledge title
        'content' => "This is where you put the detailed knowledge content.\n" .
                     "You can format it with multiple lines like this.\n" .
                     "Include all relevant information here.",
        'usage_instructions' => 'Explain when this knowledge should be used by the AI'
    ];
}

// To use this template:
// 1. Copy this file and rename it (e.g., my_topic.php)
// 2. Rename the function to match your topic (e.g., getMyTopicKnowledge)
// 3. Fill in the title, content, and usage instructions
// 4. Add the file to knowledge_manager.php by:
//    - Adding require_once __DIR__ . '/my_topic.php'; at the top
//    - Adding $this->knowledgeBase['my_topic'] = getMyTopicKnowledge(); in loadKnowledgeSources()
