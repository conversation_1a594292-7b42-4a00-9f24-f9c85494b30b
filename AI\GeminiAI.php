<?php
require_once 'config.php';
require_once '../includes/session.php';
require_once '../query/ai.qry';
require_once '../core/dbcon.ini';
require_once 'ai_knowledge.php';

class GeminiAI {

    function generateResponse($message) {
        global $server1, $username1, $password1, $database1;
        $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $aifnc = new AIFNC();

        $firstname = $_SESSION['firstname'] ?? '';
        $lastname = $_SESSION['lastname'] ?? '';
        $role = $_SESSION['role'] ?? '';
        $modid = $_SESSION['modid'] ?? 1; // Default to Staff if not set

        // Get user permissions
        $permissions = $aifnc->get_user_permissions($modid);

        $systemPrompt = getAISystemPrompt();

        if ($firstname) {
            $systemPrompt .= "\n\nCurrent user: " . $firstname . " " . $lastname;
            $systemPrompt .= " (Role: " . $permissions['role_name'] . ")";

            // Add role-specific instructions to the AI
            if ($modid == 0) {
                $systemPrompt .= "\n\nUser Access Level: ADMINISTRATOR - You can provide information about students, faculty, user accounts, and administrative functions.";
            } else {
                $systemPrompt .= "\n\nUser Access Level: STAFF - You can only provide information about students and basic functions. Do not provide user account details, passwords, or administrative information.";
            }
        }

        $historyContext = "";
        if (isset($_SESSION['chat_history'])) {
            foreach ($_SESSION['chat_history'] as $chat) {
                $historyContext .= "Human: " . $chat['user'] . "\n";
                $historyContext .= $chat['ai'] . "\n\n";
            }
        }

        $fullContext = $systemPrompt . "\n\n" . $historyContext . "User: " . $message;

        // Check for quick search commands first
        $quickSearchResult = $this->handleQuickCommands($db, $message, $aifnc, $modid, $permissions);
        if ($quickSearchResult) {
            $fullContext .= "\n\n" . $quickSearchResult;
        } else {
            // Check for "show all students" requests
            if (preg_match('/show|list|pakita|lahat|all.*students?/i', $message)) {
                if ($modid == 0) { // Admin can see all students
                    $allStudents = $aifnc->get_student_data($db, $modid);
                    $fullContext .= "\n\n" . $allStudents;
                } else { // Staff cannot see all students
                    $fullContext .= "\n\nAccess Restricted: Staff users cannot view the complete list of all students for privacy reasons. You can search for specific students by name instead.";
                }
            } else {
                // Smart search functionality with role-based access control
                $searchResults = $this->performSmartSearch($db, $message, $aifnc, $modid, $permissions);
                if ($searchResults) {
                    $fullContext .= "\n\n" . $searchResults;
                }
            }
        }

        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        ["text" => $fullContext]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        curl_close($ch);

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'message' => $result['candidates'][0]['content']['parts'][0]['text']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'API error'
            ];
        }
    }

    function handleQuickCommands($db, $message, $aifnc, $modid, $permissions) {
        $message = trim($message);

        // Quick search commands - no need to type "search"
        if (preg_match('/^\/(\w+)(.*)/', $message, $matches)) {
            $command = strtolower($matches[1]);
            $query = trim($matches[2]);

            switch ($command) {
                case 's':
                case 'search':
                    return $this->performSmartSearch($db, $query, $aifnc, $modid, $permissions);

                case 'student':
                case 'std':
                    $students = $aifnc->search_student_by_name($db, $query, $modid);
                    if (!empty($students)) {
                        $result = "Students matching '$query':\n";
                        foreach ($students as $student) {
                            $result .= "- " . ($student['Last_Name'] ?? 'N/A') . " " . ($student['First_Name'] ?? 'N/A') .
                                      " (Course: " . ($student['Course'] ?? 'N/A') . ", Year: " . ($student['Year'] ?? 'N/A') . ")\n";
                        }
                        return $result;
                    } else {
                        return "No students found matching '$query'.";
                    }

                case 'course':
                    $students = $aifnc->search_student_by_course($db, $query, $modid);
                    if (!empty($students)) {
                        $result = "Students in course '$query' (" . count($students) . " found):\n";
                        $count = 0;
                        foreach ($students as $student) {
                            if ($count < 10) {
                                $result .= "- " . ($student['Last_Name'] ?? 'N/A') . " " . ($student['First_Name'] ?? 'N/A') .
                                          " (Course: " . ($student['Course'] ?? 'N/A') . ", Year: " . ($student['Year'] ?? 'N/A') . ")\n";
                                $count++;
                            }
                        }
                        if (count($students) > 10) {
                            $result .= "... and " . (count($students) - 10) . " more students";
                        }
                        return $result;
                    } else {
                        return "No students found in course '$query'.";
                    }

                case 'help':
                case 'commands':
                    $helpText = "Quick Search Commands:\n";
                    $helpText .= "/s [name] - Search for students or faculty\n";
                    $helpText .= "/student [name] - Search students only\n";
                    $helpText .= "/course [course] - Search by course\n";
                    if ($permissions['can_view_users']) {
                        $helpText .= "/user [name] - Search faculty/staff (Admin only)\n";
                    }
                    $helpText .= "/help - Show this help\n\n";
                    $helpText .= "You can also just type a name directly (e.g., 'nessa') to search!";
                    return $helpText;

                case 'user':
                case 'faculty':
                    if ($permissions['can_view_users']) {
                        $users = $aifnc->search_user_by_name($db, $query, $modid);
                        if (!empty($users)) {
                            $result = "Faculty/Staff matching '$query':\n";
                            foreach ($users as $user) {
                                $result .= "- " . ($user['fullname'] ?? 'N/A') .
                                          " (Position: " . ($user['position'] ?? 'N/A') .
                                          ", Status: " . (($user['status'] ?? 0) == 0 ? 'Active' : 'Inactive') . ")\n";
                            }
                            return $result;
                        } else {
                            return "No faculty/staff found matching '$query'.";
                        }
                    } else {
                        return "Access Denied: Staff users cannot search for faculty/staff information.";
                    }

                default:
                    return "Unknown command '/$command'. Type '/help' for available commands.";
            }
        }

        return null; // No quick command found
    }

    function performSmartSearch($db, $message, $aifnc, $modid, $permissions) {
        $searchResult = "";

        // Check if user is searching for a name - improved detection
        $isSearchCommand = false;

        // Direct search commands
        if (preg_match('/^(search|find|look for|hanap|hinahanap)\s+/i', $message)) {
            $isSearchCommand = true;
        }

        // Single word that could be a name (no greeting words)
        if (!preg_match('/^(hi|hello|hey|good|how|what|when|where|why|kamusta|kumusta)/i', $message) &&
            preg_match('/^[a-zA-Z\s]{2,}$/', trim($message)) &&
            str_word_count(trim($message)) <= 3) {
            $isSearchCommand = true;
        }

        if ($isSearchCommand) {

            // Extract potential names from the message
            $cleanMessage = preg_replace('/^(search|find|look for|hanap|hinahanap)\s+/i', '', $message);
            $words = explode(' ', $cleanMessage);
            $potentialNames = array();

            foreach ($words as $word) {
                $cleanWord = preg_replace('/[^a-zA-Z]/', '', $word);
                if (strlen($cleanWord) > 1 && !in_array(strtolower($cleanWord), ['student', 'user', 'name', 'ang', 'yung', 'the', 'na', 'sa', 'ng'])) {
                    $potentialNames[] = $cleanWord;
                }
            }

            // If no names extracted but message looks like a name, use the whole message
            if (empty($potentialNames) && preg_match('/^[a-zA-Z\s]{2,}$/', trim($cleanMessage))) {
                $potentialNames[] = trim($cleanMessage);
            }

            foreach ($potentialNames as $name) {
                // Check if user is searching for themselves
                $currentUserFirstName = $_SESSION['firstname'] ?? '';
                $currentUserLastName = $_SESSION['lastname'] ?? '';
                $currentUserFullName = trim($currentUserFirstName . ' ' . $currentUserLastName);

                if (stripos($currentUserFullName, $name) !== false || stripos($name, $currentUserFirstName) !== false) {
                    $searchResult .= "You are currently logged in as: $currentUserFullName\n";
                    $searchResult .= "Role: " . $permissions['role_name'] . "\n";
                    if (isset($_SESSION['position'])) {
                        $searchResult .= "Position: " . $_SESSION['position'] . "\n";
                    }
                    $searchResult .= "\nIf you're looking for someone else with a similar name, please be more specific.\n\n";
                    continue; // Skip searching for the current user
                }

                // Search students - Both Admin and Staff can search students
                $students = $aifnc->search_student_by_name($db, $name, $modid);
                if (!empty($students)) {
                    $filteredStudents = $aifnc->filter_student_data_by_role($students, $modid);
                    $searchResult .= "Found Students matching '$name':\n";
                    foreach ($filteredStudents as $student) {
                        $searchResult .= "- Name: " . ($student['Last_Name'] ?? 'N/A') . " " .
                                                     ($student['First_Name'] ?? 'N/A') .
                                        ", Course: " . ($student['Course'] ?? 'N/A') .
                                        ", Year: " . ($student['Year'] ?? 'N/A') .
                                        ", Student ID: " . ($student['Student_ID'] ?? 'N/A') . "\n";
                    }
                    $searchResult .= "\n";
                } else {
                    $searchResult .= "No students found matching '$name' in the database.\n\n";
                }

                // Search users/faculty - Only Admin can search user accounts
                if ($permissions['can_view_users']) {
                    $users = $aifnc->search_user_by_name($db, $name, $modid);
                    if (!empty($users)) {
                        $searchResult .= "Found Faculty/Staff matching '$name':\n";
                        foreach ($users as $user) {
                            $searchResult .= "- Name: " . ($user['fullname'] ?? 'N/A') .
                                            ", Username: " . ($user['username'] ?? 'N/A') .
                                            ", Position: " . ($user['position'] ?? 'N/A') .
                                            ", Status: " . (($user['status'] ?? 0) == 0 ? 'Active' : 'Inactive') . "\n";
                        }
                        $searchResult .= "\n";
                    }
                } else {
                    // Staff users trying to search for faculty/staff
                    if (preg_match('/faculty|staff|teacher|instructor|admin|user/i', $message)) {
                        $searchResult .= "Access Denied: Staff users cannot search for faculty/staff information. You can only search for student data.\n\n";
                    }
                }
            }
        }

        // Check if user is searching by student ID - Both Admin and Staff can search students
        if (preg_match('/\b\d{4,}\b/', $message, $matches)) {
            $studentId = $matches[0];
            $students = $aifnc->search_student_by_id($db, $studentId, $modid);
            if (!empty($students)) {
                $filteredStudents = $aifnc->filter_student_data_by_role($students, $modid);
                $searchResult .= "Found Students with ID '$studentId':\n";
                foreach ($filteredStudents as $student) {
                    $searchResult .= "- Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                                    ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                                    ", Course: " . ($student['Course'] ?? 'N/A') .
                                    ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                }
                $searchResult .= "\n";
            }
        }

        // Check if user is searching by course - Both Admin and Staff can search students
        if (preg_match('/course|program|BSIT|BSCS|BSBA|Engineering|Education/i', $message, $matches)) {
            $course = $matches[0];
            $students = $aifnc->search_student_by_course($db, $course, $modid);
            if (!empty($students)) {
                $filteredStudents = $aifnc->filter_student_data_by_role($students, $modid);
                $searchResult .= "Found Students in course '$course':\n";
                $count = 0;
                foreach ($filteredStudents as $student) {
                    if ($count < 10) { // Limit to 10 results
                        $searchResult .= "- Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                                        ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                                        ", Course: " . ($student['Course'] ?? 'N/A') .
                                        ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                        $count++;
                    }
                }
                if (count($filteredStudents) > 10) {
                    $searchResult .= "... and " . (count($filteredStudents) - 10) . " more students\n";
                }
                $searchResult .= "\n";
            }
        }

        // Check for admin-only queries and block them for staff users
        if ($modid != 0) { // Staff users
            if (preg_match('/password|admin|delete|remove|modify user|change user|user account|login|credential/i', $message)) {
                $searchResult .= "Access Denied: Staff users cannot access administrative functions or user account information. Please contact an administrator for assistance.\n\n";
            }
        }

        return $searchResult;
    }

    function formatResponse($response) {
        if (!$response['success']) {
            return [
                'type' => 'error',
                'message' => $response['error']
            ];
        }

        $message = $response['message'];
        $message = preg_replace('/\*+/', '', $message);
        $message = preg_replace('/\n\n+/', "\n\n", $message);

        $userMessage = $_SESSION['current_user_message'] ?? '';

        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }

        $_SESSION['chat_history'][] = [
            'user' => $userMessage,
            'ai' => $message
        ];

        if (count($_SESSION['chat_history']) > 5) {
            array_shift($_SESSION['chat_history']);
        }

        return [
            'type' => 'success',
            'message' => $message
        ];
    }

    function analyzeDocument($filePath, $mimeType, $userMessage = '') {
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return [
                'success' => false,
                'error' => 'Failed to read file'
            ];
        }

        $base64Content = base64_encode($fileContent);
        $prompt = $userMessage ? $userMessage : "Please analyze this document and provide key insights.";

        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "inlineData" => [
                                "mimeType" => $mimeType,
                                "data" => $base64Content
                            ]
                        ],
                        [
                            "text" => $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        curl_close($ch);

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'message' => $result['candidates'][0]['content']['parts'][0]['text']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'API error'
            ];
        }
    }
}
?>
