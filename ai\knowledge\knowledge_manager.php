<?php
/**
 * Knowledge Manager
 * This file manages loading and combining knowledge from different sources
 */

// Include all knowledge files
require_once __DIR__ . '/core_prompt.php';
require_once __DIR__ . '/don_bosco.php';
// Add more knowledge files as needed in the future

class KnowledgeManager {
    private $knowledgeBase = [];
    
    public function __construct() {
        // Load all knowledge sources
        $this->loadKnowledgeSources();
    }
    
    private function loadKnowledgeSources() {
        // Load core system prompt
        $this->knowledgeBase['core'] = getCoreSystemPrompt();
        
        // Load Don Bosco knowledge
        $this->knowledgeBase['don_bosco'] = getDonBoscoKnowledge();
        
        // Add more knowledge sources here as they are created
    }
    
    public function getFullSystemPrompt($studentContext = '') {
        $systemPrompt = $this->knowledgeBase['core']['content'];
        
        // Add all knowledge bases with their usage instructions
        foreach ($this->knowledgeBase as $key => $knowledge) {
            if ($key !== 'core') { // Skip core as it's already included
                $systemPrompt .= "\n\n" . $knowledge['title'] . " (" . $knowledge['usage_instructions'] . "):\n";
                $systemPrompt .= $knowledge['content'];
            }
        }
        
        // Add student data if provided
        if (!empty($studentContext)) {
            $systemPrompt .= "\n\nStudent Data:\n" . $studentContext;
        }
        
        // Add final instruction
        $systemPrompt .= "\n\nKeep responses friendly and helpful while maintaining professionalism appropriate for an educational setting.";
        
        return $systemPrompt;
    }
    
    /**
     * Add a new knowledge source to the system
     * @param string $key Unique identifier for this knowledge
     * @param array $knowledge Knowledge array with title, content, and usage_instructions
     */
    public function addKnowledge($key, $knowledge) {
        $this->knowledgeBase[$key] = $knowledge;
    }
    
    /**
     * Get a specific knowledge source
     * @param string $key The knowledge identifier
     * @return array|null The knowledge array or null if not found
     */
    public function getKnowledge($key) {
        return isset($this->knowledgeBase[$key]) ? $this->knowledgeBase[$key] : null;
    }
    
    /**
     * Get all available knowledge keys
     * @return array List of all knowledge keys
     */
    public function getAvailableKnowledge() {
        return array_keys($this->knowledgeBase);
    }
}
