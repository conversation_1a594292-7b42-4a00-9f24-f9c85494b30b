<?php
require_once 'config.php';
require_once '../includes/session.php';
require_once '../query/ai.qry';
require_once '../core/dbcon.ini';
require_once 'ai_knowledge.php';

class GeminiAI {

    function generateResponse($message) {
        global $server1, $username1, $password1, $database1;
        $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $aifnc = new AIFNC();

        $firstname = $_SESSION['firstname'] ?? '';
        $lastname = $_SESSION['lastname'] ?? '';
        $role = $_SESSION['role'] ?? '';

        $systemPrompt = getAISystemPrompt();

        if ($firstname) {
            $systemPrompt .= "\n\nCurrent user: " . $firstname . " " . $lastname;
            if ($role) {
                $systemPrompt .= " (Role: " . $role . ")";
            }
        }

        $historyContext = "";
        if (isset($_SESSION['chat_history'])) {
            foreach ($_SESSION['chat_history'] as $chat) {
                $historyContext .= "Human: " . $chat['user'] . "\n";
                $historyContext .= $chat['ai'] . "\n\n";
            }
        }

        $fullContext = $systemPrompt . "\n\n" . $historyContext . "User: " . $message;

        // Smart search functionality
        $searchResults = $this->performSmartSearch($db, $message, $aifnc);
        if ($searchResults) {
            $fullContext .= "\n\n" . $searchResults;
        }

        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        ["text" => $fullContext]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        curl_close($ch);

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'message' => $result['candidates'][0]['content']['parts'][0]['text']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'API error'
            ];
        }
    }

    function performSmartSearch($db, $message, $aifnc) {
        $searchResult = "";

        // Check if user is searching for a name
        if (preg_match('/search|find|look for|hanap|hinahanap/i', $message)) {

            // Extract potential names from the message
            $words = explode(' ', $message);
            $potentialNames = array();

            foreach ($words as $word) {
                $cleanWord = preg_replace('/[^a-zA-Z]/', '', $word);
                if (strlen($cleanWord) > 2 && !in_array(strtolower($cleanWord), ['search', 'find', 'look', 'for', 'hanap', 'hinahanap', 'student', 'user', 'name', 'ang', 'yung', 'the'])) {
                    $potentialNames[] = $cleanWord;
                }
            }

            foreach ($potentialNames as $name) {
                // Search students
                $students = $aifnc->search_student_by_name($db, $name);
                if (!empty($students)) {
                    $searchResult .= "Found Students matching '$name':\n";
                    foreach ($students as $student) {
                        $searchResult .= "- Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                                        ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                                        ", Course: " . ($student['Course'] ?? 'N/A') .
                                        ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                    }
                    $searchResult .= "\n";
                }

                // Search users/faculty
                $users = $aifnc->search_user_by_name($db, $name);
                if (!empty($users)) {
                    $searchResult .= "Found Faculty/Staff matching '$name':\n";
                    foreach ($users as $user) {
                        $searchResult .= "- Name: " . ($user['fullname'] ?? 'N/A') .
                                        ", Username: " . ($user['username'] ?? 'N/A') .
                                        ", Position: " . ($user['position'] ?? 'N/A') . "\n";
                    }
                    $searchResult .= "\n";
                }
            }
        }

        // Check if user is searching by student ID
        if (preg_match('/\b\d{4,}\b/', $message, $matches)) {
            $studentId = $matches[0];
            $students = $aifnc->search_student_by_id($db, $studentId);
            if (!empty($students)) {
                $searchResult .= "Found Students with ID '$studentId':\n";
                foreach ($students as $student) {
                    $searchResult .= "- Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                                    ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                                    ", Course: " . ($student['Course'] ?? 'N/A') .
                                    ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                }
                $searchResult .= "\n";
            }
        }

        // Check if user is searching by course
        if (preg_match('/course|program|BSIT|BSCS|BSBA|Engineering|Education/i', $message, $matches)) {
            $course = $matches[0];
            $students = $aifnc->search_student_by_course($db, $course);
            if (!empty($students)) {
                $searchResult .= "Found Students in course '$course':\n";
                $count = 0;
                foreach ($students as $student) {
                    if ($count < 10) { // Limit to 10 results
                        $searchResult .= "- Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                                        ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                                        ", Course: " . ($student['Course'] ?? 'N/A') .
                                        ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                        $count++;
                    }
                }
                if (count($students) > 10) {
                    $searchResult .= "... and " . (count($students) - 10) . " more students\n";
                }
                $searchResult .= "\n";
            }
        }

        return $searchResult;
    }

    function formatResponse($response) {
        if (!$response['success']) {
            return [
                'type' => 'error',
                'message' => $response['error']
            ];
        }

        $message = $response['message'];
        $message = preg_replace('/\*+/', '', $message);
        $message = preg_replace('/\n\n+/', "\n\n", $message);

        $userMessage = $_SESSION['current_user_message'] ?? '';

        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }

        $_SESSION['chat_history'][] = [
            'user' => $userMessage,
            'ai' => $message
        ];

        if (count($_SESSION['chat_history']) > 5) {
            array_shift($_SESSION['chat_history']);
        }

        return [
            'type' => 'success',
            'message' => $message
        ];
    }

    function analyzeDocument($filePath, $mimeType, $userMessage = '') {
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return [
                'success' => false,
                'error' => 'Failed to read file'
            ];
        }

        $base64Content = base64_encode($fileContent);
        $prompt = $userMessage ? $userMessage : "Please analyze this document and provide key insights.";

        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "inlineData" => [
                                "mimeType" => $mimeType,
                                "data" => $base64Content
                            ]
                        ],
                        [
                            "text" => $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);
        curl_close($ch);

        $result = json_decode($response, true);

        if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
            return [
                'success' => true,
                'message' => $result['candidates'][0]['content']['parts'][0]['text']
            ];
        } else {
            return [
                'success' => false,
                'error' => 'API error'
            ];
        }
    }
}
?>
