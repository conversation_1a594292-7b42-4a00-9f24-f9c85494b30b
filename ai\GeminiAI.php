<?php
require_once 'config.php';
require_once '../includes/session.php';
require_once '../query/access.qry';
require_once '../query/students.qry';
require_once '../core/dbcon.ini';
require_once 'ai_knowledge.php';

class GeminiAI {
    private $api_key;
    private $model;
    private $api_url;

    public function __construct() {
        $this->api_key = GEMINI_API_KEY;
        $this->model = GEMINI_MODEL;
        $this->api_url = GEMINI_API_URL;
    }

    private function getChatHistory() {
        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }
        return $_SESSION['chat_history'];
    }

    private function addChatHistory($userMessage, $aiResponse) {
        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }

        $_SESSION['chat_history'][] = [
            'user' => $userMessage,
            'ai' => $aiResponse
        ];

        // Keep only last 5 messages
        if (count($_SESSION['chat_history']) > 5) {
            array_shift($_SESSION['chat_history']);
        }
    }

    private function buildHistoryContext() {
        $history = $this->getChatHistory();
        $context = "";

        foreach ($history as $chat) {
            $context .= "Human: " . $chat['user'] . "\n";
            $context .= $chat['ai'] . "\n\n";
        }

        return $context;
    }

    public function generateResponse($message, $sessionContext = []) {
        $url = $this->api_url . $this->model . ":generateContent?key=" . $this->api_key;

        // Get user info
        $firstname = $_SESSION['firstname'] ?? '';
        $lastname = $_SESSION['lastname'] ?? '';
        $role = $_SESSION['role'] ?? '';

        // Get system prompt
        $systemPrompt = getAISystemPrompt();

        // Add user context
        if ($firstname) {
            $systemPrompt .= "\n\nCurrent user: " . $firstname . " " . $lastname;
            if ($role) {
                $systemPrompt .= " (Role: " . $role . ")";
            }
        }

        $historyContext = $this->buildHistoryContext();
        $fullContext = $systemPrompt . "\n\n" . $historyContext . "User: " . $message;

        // Add student data if needed
        if (stripos($message, 'student') !== false) {
            $studentData = $this->getStudentData();
            if ($studentData) {
                $fullContext .= "\n\n" . $studentData;
            }
        }

        return $this->callGeminiAPI($fullContext);
    }

    private function getStudentData() {
        try {
            global $server1, $username1, $password1, $database1;
            $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
            $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $studentsFnc = new STUDENTSFNC();
            $students = $studentsFnc->get_allstudents($db);

            $studentInfo = "Available Student Data:\n";
            foreach ($students as $student) {
                $studentInfo .= "Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                               ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                               ", Course: " . ($student['Course'] ?? 'N/A') .
                               ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
            }
            return $studentInfo;
        } catch (Exception $e) {
            error_log("Database error: " . $e->getMessage());
            return null;
        }
    }

    private function callGeminiAPI($context) {
        $url = $this->api_url . $this->model . ":generateContent?key=" . $this->api_key;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        ["text" => $context]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }

            curl_close($ch);

            $result = json_decode($response, true);

            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }

            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    public function formatResponse($response) {
        if (!$response['success']) {
            return [
                'type' => 'error',
                'message' => $response['error']
            ];
        }

        $message = $response['message'];

        // Clean up formatting
        $message = preg_replace('/\*+/', '', $message);
        $message = preg_replace('/\n\n+/', "\n\n", $message);

        // Save to history
        $userMessage = $_SESSION['current_user_message'] ?? '';
        $this->addChatHistory($userMessage, $message);

        return [
            'type' => 'success',
            'message' => $message
        ];
    }

    public function analyzeDocument($filePath, $mimeType, $userMessage = '') {
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return [
                'success' => false,
                'error' => 'Failed to read file'
            ];
        }

        $base64Content = base64_encode($fileContent);
        $prompt = $userMessage ? $userMessage : "Please analyze this document and provide key insights in a clear, organized format.";

        $url = $this->api_url . $this->model . ":generateContent?key=" . $this->api_key;

        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "inlineData" => [
                                "mimeType" => $mimeType,
                                "data" => $base64Content
                            ]
                        ],
                        [
                            "text" => $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

            $response = curl_exec($ch);

            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }

            curl_close($ch);

            $result = json_decode($response, true);

            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }

            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
