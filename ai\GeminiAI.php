<?php
require_once 'config.php';
require_once '../includes/session.php';
require_once '../query/ai.qry';
require_once '../query/students.qry';
require_once '../core/dbcon.ini';
require_once 'ai_knowledge.php';

class GeminiAI {

    function generateResponse($message) {
        global $server1, $username1, $password1, $database1;
        $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
        $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $aifnc = new AIFNC();

        $firstname = $_SESSION['firstname'] ?? '';
        $lastname = $_SESSION['lastname'] ?? '';
        $role = $_SESSION['role'] ?? '';

        $systemPrompt = getAISystemPrompt();

        if ($firstname) {
            $systemPrompt .= "\n\nCurrent user: " . $firstname . " " . $lastname;
            if ($role) {
                $systemPrompt .= " (Role: " . $role . ")";
            }
        }

        $historyContext = $aifnc->build_history_context();
        $fullContext = $systemPrompt . "\n\n" . $historyContext . "User: " . $message;

        if (stripos($message, 'student') !== false) {
            $studentData = $aifnc->get_student_data($db);
            if ($studentData) {
                $fullContext .= "\n\n" . $studentData;
            }
        }

        return $aifnc->call_gemini_api($fullContext);
    }

    function formatResponse($response) {
        $aifnc = new AIFNC();
        return $aifnc->format_response($response);
    }

    function analyzeDocument($filePath, $mimeType, $userMessage = '') {
        $aifnc = new AIFNC();
        return $aifnc->analyze_document($filePath, $mimeType, $userMessage);
    }
}
?>
