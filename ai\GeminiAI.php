<?php
require_once 'config.php';
require_once '../includes/session.php';
require_once '../query/access.qry';
require_once '../query/students.qry';
require_once '../core/dbcon.ini';
require_once 'knowledge/knowledge_manager.php';

class GeminiAI {
    private $api_key;
    private $model;
    private $api_url;
    private $knowledgeManager;
    
    public function __construct() {
        $this->api_key = GEMINI_API_KEY;
        $this->model = GEMINI_MODEL;
        $this->api_url = GEMINI_API_URL;
        $this->knowledgeManager = new KnowledgeManager();
    }
    
    private function initializeChatHistory() {
        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }
    }

    private function addToHistory($userMessage, $aiResponse) {
        $this->initializeChatHistory();
        
        // Store exact messages without modification
        $_SESSION['chat_history'][] = [
            'user' => $userMessage,
            'ai' => $aiResponse
        ];
        
        // Keep only last 5 messages for better context focus
        if (count($_SESSION['chat_history']) > 5) {
            array_shift($_SESSION['chat_history']);
        }
    }

    private function getHistoryContext() {
        $this->initializeChatHistory();
        $context = "";
        
        // Include all recent history for accurate context
        foreach ($_SESSION['chat_history'] as $exchange) {
            $context .= "Human: " . $exchange['user'] . "\n";
            $context .= $exchange['ai'] . "\n\n";
        }
        
        return $context;
    }

    public function generateResponse($message, $sessionContext = []) {
        $url = $this->api_url . $this->model . ":generateContent?key=" . $this->api_key;

        // Removed access control checks as per user feedback - assume all chat users are staff/admin.
        
        // Get system data
        // Define $isAdmin and $isStaff based on session for potential contextual use, but not for blocking access.
        $modid = $sessionContext['modid'] ?? $_SESSION['modid'] ?? null;
        $role = $sessionContext['role'] ?? $_SESSION['role'] ?? null;
        $usertype = $sessionContext['usertype'] ?? $_SESSION['usertype'] ?? null;
        $isAdmin = ($modid === 0) || (stripos($role ?? '', 'admin') !== false) || (stripos($usertype ?? '', 'admin') !== false);
        $isStaff = ($modid === 1) || (stripos($role ?? '', 'staff') !== false) || (stripos($usertype ?? '', 'staff') !== false);

        $access = new ACCESS();
        
        // Get system prompt from knowledge manager
        $systemPrompt = $this->knowledgeManager->getFullSystemPrompt("");

        // Add current context to the prompt
        if (isset($_SESSION['firstname'])) {
            $systemPrompt .= "\n\nCurrent user: " . $_SESSION['firstname'] . " " . $_SESSION['lastname'];
            // Add role context based on the determined role
            if ($isAdmin) {
                $systemPrompt .= " (Role: Administrator)";
            } elseif ($isStaff) {
                $systemPrompt .= " (Role: Staff)";
            }
        }

        $historyContext = $this->getHistoryContext();
        $finalUserMessage = "User: " . $message;
        $fullContext = $systemPrompt . "\n\n" . $historyContext . $finalUserMessage;

        // Check for student-related questions
        if (stripos($message, 'list of students') !== false || stripos($message, 'student information') !== false || stripos($message, 'students') !== false) {
            try {
                global $server1, $username1, $password1, $database1;
                $db = new PDO("mysql:host=" . $server1 . ";dbname=" . $database1 . ";charset=utf8", $username1, $password1);
                $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                $studentsFnc = new STUDENTSFNC();
                $studentsData = $studentsFnc->get_allstudents($db);
                
                $studentInfo = "Available Student Data:\n";
                foreach ($studentsData as $student) {
                    $studentInfo .= "Student ID: " . ($student['Student_ID'] ?? 'N/A') . ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') . ", Course: " . ($student['Course'] ?? 'N/A') . ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
                }
                $fullContext .= "\n\n" . $studentInfo;
            } catch (PDOException $e) {
                error_log("Database error in GeminiAI: " . $e->getMessage());
                return [
                    'success' => false,
                    'error' => 'Database error retrieving student data: ' . $e->getMessage()
                ];
            }
        }
        
        // Check for attendance-related questions
        if (stripos($message, 'attendance') !== false || stripos($message, 'present') !== false || stripos($message, 'time in') !== false) {
            // No specific action needed here, as fullContext is already built.
            // This block can be used for future attendance-specific data retrieval if needed.
        }

        $data = [
            "contents" => [
                [
                    "parts" => [
                        ["text" => $fullContext]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];
        
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $result = json_decode($response, true);
            
            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }
            
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    public function formatResponse($response) {
        if (!$response['success']) {
            return [
                'type' => 'error',
                'message' => $response['error']
            ];
        }

        // Clean up response formatting
        $message = $response['message'];
        
        // Remove asterisks and markdown formatting
        $message = preg_replace('/\*+/', '', $message);
        
        // Convert double newlines to proper spacing
        $message = preg_replace('/\n\n+/', "\n\n", $message);
        
        // Get the user message from the session before storing history
        $userMessage = isset($_SESSION['current_user_message']) ? $_SESSION['current_user_message'] : '';
        $this->addToHistory($userMessage, $message);
        
        return [
            'type' => 'success',
            'message' => $message
        ];
    }

    public function analyzeDocument($filePath, $mimeType, $userMessage = '') {
        $url = $this->api_url . $this->model . ":generateContent?key=" . $this->api_key;

        // Read file contents
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return [
                'success' => false,
                'error' => 'Failed to read file'
            ];
        }

        // Convert to base64
        $base64Content = base64_encode($fileContent);

        // Build prompt based on user message
        $prompt = $userMessage
            ? $userMessage
            : "Please analyze this document and provide key insights in a clear, organized format. For PDFs, extract and summarize the main points. For images, describe the content and any text present.";

        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "inlineData" => [
                                "mimeType" => $mimeType,
                                "data" => $base64Content
                            ]
                        ],
                        [
                            "text" => $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);
            
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $result = json_decode($response, true);
            
            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }
            
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
