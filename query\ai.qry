<?php
class AIFNC
{
    function get_allstudents($db)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_student_data($db)
    {
        $students = $this->get_allstudents($db);
        $studentInfo = "Available Student Data:\n";

        foreach ($students as $student) {
            $studentInfo .= "Student ID: " . ($student['Student_ID'] ?? 'N/A') .
                           ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') .
                           ", Course: " . ($student['Course'] ?? 'N/A') .
                           ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
        }
        return $studentInfo;
    }

    function search_student_by_name($db, $name)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE First_Name LIKE ? OR Last_Name LIKE ? OR CONCAT(First_Name, ' ', Last_Name) LIKE ?");
        $searchTerm = '%' . $name . '%';
        $stmt->execute(array($searchTerm, $searchTerm, $searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function search_user_by_name($db, $name)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM useraccounts WHERE firstname LIKE ? OR lastname LIKE ? OR fullname LIKE ?");
        $searchTerm = '%' . $name . '%';
        $stmt->execute(array($searchTerm, $searchTerm, $searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function search_student_by_id($db, $id)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE Student_ID LIKE ?");
        $searchTerm = '%' . $id . '%';
        $stmt->execute(array($searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function search_student_by_course($db, $course)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE Course LIKE ?");
        $searchTerm = '%' . $course . '%';
        $stmt->execute(array($searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }


}
?>
