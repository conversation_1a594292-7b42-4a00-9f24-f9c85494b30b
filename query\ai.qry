<?php
class AIFNC
{
    function get_chat_history($session_id)
    {
        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }
        return $_SESSION['chat_history'];
    }

    function add_chat_history($userMessage, $aiResponse)
    {
        if (!isset($_SESSION['chat_history'])) {
            $_SESSION['chat_history'] = [];
        }
        
        $_SESSION['chat_history'][] = [
            'user' => $userMessage,
            'ai' => $aiResponse
        ];
        
        if (count($_SESSION['chat_history']) > 5) {
            array_shift($_SESSION['chat_history']);
        }
    }

    function build_history_context()
    {
        $history = $this->get_chat_history('');
        $context = "";
        
        foreach ($history as $chat) {
            $context .= "Human: " . $chat['user'] . "\n";
            $context .= $chat['ai'] . "\n\n";
        }
        
        return $context;
    }

    function get_student_data($db)
    {
        try {
            $studentsFnc = new STUDENTSFNC();
            $students = $studentsFnc->get_allstudents($db);
            
            $studentInfo = "Available Student Data:\n";
            foreach ($students as $student) {
                $studentInfo .= "Student ID: " . ($student['Student_ID'] ?? 'N/A') . 
                               ", Name: " . ($student['First_Name'] ?? 'N/A') . " " . ($student['Last_Name'] ?? 'N/A') . 
                               ", Course: " . ($student['Course'] ?? 'N/A') . 
                               ", Year: " . ($student['Year'] ?? 'N/A') . "\n";
            }
            return $studentInfo;
        } catch (Exception $e) {
            return null;
        }
    }

    function call_gemini_api($context)
    {
        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;
        
        $data = [
            "contents" => [
                [
                    "parts" => [
                        ["text" => $context]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];
        
        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $result = json_decode($response, true);
            
            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }
            
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    function format_response($response)
    {
        if (!$response['success']) {
            return [
                'type' => 'error',
                'message' => $response['error']
            ];
        }

        $message = $response['message'];
        
        $message = preg_replace('/\*+/', '', $message);
        $message = preg_replace('/\n\n+/', "\n\n", $message);
        
        $userMessage = $_SESSION['current_user_message'] ?? '';
        $this->add_chat_history($userMessage, $message);
        
        return [
            'type' => 'success',
            'message' => $message
        ];
    }

    function analyze_document($filePath, $mimeType, $userMessage = '')
    {
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return [
                'success' => false,
                'error' => 'Failed to read file'
            ];
        }

        $base64Content = base64_encode($fileContent);
        $prompt = $userMessage ? $userMessage : "Please analyze this document and provide key insights in a clear, organized format.";

        $url = GEMINI_API_URL . GEMINI_MODEL . ":generateContent?key=" . GEMINI_API_KEY;
        
        $data = [
            "contents" => [
                [
                    "parts" => [
                        [
                            "inlineData" => [
                                "mimeType" => $mimeType,
                                "data" => $base64Content
                            ]
                        ],
                        [
                            "text" => $prompt
                        ]
                    ]
                ]
            ],
            "generationConfig" => [
                "temperature" => 0.7,
                "topK" => 40,
                "topP" => 0.95,
                "maxOutputTokens" => 1024,
            ]
        ];

        try {
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
            
            $response = curl_exec($ch);
            
            if (curl_errno($ch)) {
                throw new Exception('Curl error: ' . curl_error($ch));
            }
            
            curl_close($ch);
            
            $result = json_decode($response, true);
            
            if (isset($result['error'])) {
                throw new Exception('API error: ' . $result['error']['message']);
            }
            
            if (isset($result['candidates'][0]['content']['parts'][0]['text'])) {
                return [
                    'success' => true,
                    'message' => $result['candidates'][0]['content']['parts'][0]['text']
                ];
            } else {
                throw new Exception('Unexpected API response format');
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
?>
