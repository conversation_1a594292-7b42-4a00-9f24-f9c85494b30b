<?php
class AIFNC
{
    function get_allstudents($db)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student ORDER BY Last_Name ASC");
        $stmt->execute();
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_student_data($db, $modid = 1)
    {
        $students = $this->get_allstudents($db);

        if (empty($students)) {
            return "No students found in database.";
        }

        $studentInfo = "Available Student Data (" . count($students) . " students):\n";

        foreach ($students as $student) {
            $studentInfo .= "Name: " . ($student['Last_Name'] ?? 'N/A') . " " .
                                     ($student['First_Name'] ?? 'N/A') .
                           ", Course: " . ($student['Course'] ?? 'N/A') .
                           ", Year: " . ($student['Year'] ?? 'N/A') .
                           ", Student ID: " . ($student['Student_ID'] ?? 'N/A') . "\n";
        }
        return $studentInfo;
    }

    function search_student_by_name($db, $name, $modid = 1)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE First_Name LIKE ? OR Last_Name LIKE ? OR CONCAT(First_Name, ' ', Last_Name) LIKE ? ORDER BY Last_Name ASC");
        $searchTerm = '%' . $name . '%';
        $stmt->execute(array($searchTerm, $searchTerm, $searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function search_user_by_name($db, $name, $modid = 1)
    {
        $data = array();

        // Only Admin (modid = 0) can search user accounts
        if ($modid == 0) {
            // Admin can see all user data but without sensitive info like passwords
            $stmt = $db->prepare("SELECT uaid, fullname, firstname, lastname, username, position, modid, status FROM useraccounts WHERE firstname LIKE ? OR lastname LIKE ? OR fullname LIKE ?");
            $searchTerm = '%' . $name . '%';
            $stmt->execute(array($searchTerm, $searchTerm, $searchTerm));
            for($i=1; $i<=$stmt->rowCount(); $i++)
                $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        // Staff (modid = 1) cannot search user accounts - return empty array

        return $data;
    }

    function search_student_by_id($db, $id, $modid = 1)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE Student_ID LIKE ? ORDER BY Last_Name ASC");
        $searchTerm = '%' . $id . '%';
        $stmt->execute(array($searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function search_student_by_course($db, $course, $modid = 1)
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM cdas_student WHERE Course LIKE ? ORDER BY Last_Name ASC");
        $searchTerm = '%' . $course . '%';
        $stmt->execute(array($searchTerm));
        for($i=1; $i<=$stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    function get_user_permissions($modid)
    {
        if ($modid == 0) {
            return [
                'can_view_users' => true,
                'can_view_admin_functions' => true,
                'can_view_sensitive_data' => true,
                'role_name' => 'Administrator'
            ];
        } else {
            return [
                'can_view_users' => false,
                'can_view_admin_functions' => false,
                'can_view_sensitive_data' => false,
                'role_name' => 'Staff'
            ];
        }
    }

    function filter_student_data_by_role($students, $modid)
    {
        // Both Admin and Staff can see student data, but we can add filtering here if needed
        // For now, both roles can see all student information
        return $students;
    }


}
?>
