<?php 
	session_start();
	include 'core/dbcon.ini';
	include 'query/access.qry';
//	include 'query/notifications.qry';

	$access = new ACCESS;
	//$notifications = new NOTIFICATIONS;

    if(isset($_POST['username'])&&(isset($_POST['password'])))
    {
		//$error_message = "";
		if($access->get_useraccountscount($db1,$_POST['username'],$_POST['password'])==1)
		{
			$userdetails=$access->get_useraccounts($db1,$_POST['username'],$_POST['password']);
            if ($userdetails['status'] == 0) { // Check if user is active 0 for active, 1 for inactive
                $name=$userdetails['username'].' '.$userdetails['password'];
                $modid=$userdetails['modid'];
                $_SESSION['modid'] = $userdetails['modid'];
			$_SESSION['name'] = $userdetails['firstname'] . ' ' . $userdetails['lastname'];
			$_SESSION['role'] = $userdetails['position'];
			$_SESSION['uaid'] = $userdetails['uaid'];
			
			//$_SESSION['firstname'] = $userdetails['firstname'];
			//$_SESSION['lastname'] = $userdetails['lastname'];
			
			//$_SESSION['usertype'] = $userdetails['position'];
			//$_SESSION['userid'] = $userdetails['uaid'];
			//$_SESSION['uaid'] = $userdetails['uaid'];
			$_SESSION['profile_picture'] = $userdetails['profilePicture'];
			$_SESSION['emp_email'] = $userdetails['emp_email'];
			$_SESSION['LAST'] = time();
		
			//if($modid==1) // Only create notifications for staff logins, not admin
			//{
				// Create login notification
			//	$notifications->create_login_notification($db1,$userdetails['uaid'],$userdetails['firstname'],$userdetails['lastname']);
			//}
			
			if($modid==0)
			{
			?>
				<script language="javascript">
				window.location.href = "itrc/index.php"
				</script>
				<?php
			}

			if($modid==1)
			{
				?>
					<script language="javascript">
					window.location.href = "staff/main.php"
					</script>
				<?php
			}
            }
		}
	}
?>

	<?php include 'assets/common/mainheader.php'; ?>
	<?php
	if (isset($_GET['timeout'])) {
		echo "<p style='color:red;'>Session expired due to inactivity. Please log in again.</p>";
	}
	?>

	<body>
    <div id="preloader">
      <div class="spinner"></div>
    </div>
    <!-- ======== Main Wrapper Start =========== -->
    
        <!-- ========== section start ========== -->
		<section class="section" style="min-height: 100vh; display: flex; align-items: center; padding: 0;">
		<div class="container">
			<div class="row justify-content-center">
				<div class="col-lg-5">
					<div class="card-style" style="margin-top: 20px;">
						<?php if(isset($_POST['username']) && isset($_POST['password']) && $access->get_useraccountscount($db1,$_POST['username'],$_POST['password'])!=1): ?>
							<div class="alert alert-danger text-center mb-25" role="alert">
								Invalid username or password!
							</div>
						<?php endif; ?>
						<div class="text-center mb-25">
						<img src="assets/images/logo/dbc.png" alt="Logo" class="mb-15" style="max-width: 100px;">
							<h4 class="mb-10">Sign In to CDAS</h4>
							<p class="text-sm">Login to your account to continue</p>
						</div>
						<form action="" method="POST">
							<div class="input-style-1">
								<label>Username</label>
								<input type="text" name="username" placeholder="Enter your username" required />
							</div>
							<div class="input-style-1">
								<label>Password</label>
								<input type="password" name="password" placeholder="Enter password" required />
							</div>

							<div class="text-center">
								<button type="submit" class="main-btn primary-btn btn-hover w-100 mb-20">Sign In</button>
							</div>
						</form>
						<div class="login-footer text-center">
							<p class="text-sm">&copy; <?php echo date('Y'); ?> CDAS. All rights reserved.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
      <script src="assets/js/main.js"></script>
	</body>
</html>
