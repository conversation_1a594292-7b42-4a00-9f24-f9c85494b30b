<?php
require_once 'GeminiAI.php';
require_once 'config.php';
require_once '../includes/session.php';

header('Content-Type: application/json');

try {
    if (!isset($_FILES['file'])) {
        throw new Exception('No file uploaded');
    }

    $file = $_FILES['file'];
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('File upload failed');
    }

    // Get user message if provided
    $userMessage = isset($_POST['message']) ? $_POST['message'] : '';

    // Validate file type
    $allowed_types = ['application/pdf', 'image/jpeg', 'image/png'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    if (!in_array($mime_type, $allowed_types)) {
        throw new Exception('Unsupported file type. Please upload PDF or images only.');
    }

    // Process with Gemini
    $ai = new GeminiAI();
    $result = $ai->analyzeDocument($file['tmp_name'], $mime_type, $userMessage);

    if ($result['success']) {
        echo json_encode([
            'type' => 'success',
            'message' => $result['message']
        ]);
    } else {
        throw new Exception($result['error']);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'type' => 'error',
        'message' => $e->getMessage()
    ]);
}